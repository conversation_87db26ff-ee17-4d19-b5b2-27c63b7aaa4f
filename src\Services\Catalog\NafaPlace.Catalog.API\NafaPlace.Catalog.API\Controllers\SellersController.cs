using Microsoft.AspNetCore.Mvc;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Seller;
using NafaPlace.Catalog.Application.Services;
using NafaPlace.Catalog.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class SellersController : ControllerBase
    {
        private readonly ISellerService _sellerService;
        private readonly CatalogDbContext _context;

        public SellersController(ISellerService sellerService, CatalogDbContext context)
        {
            _sellerService = sellerService;
            _context = context;
        }

        /// <summary>
        /// Récupère la liste de tous les vendeurs
        /// </summary>
        /// <returns>Liste des vendeurs</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllSellers()
        {
            try
            {
                var sellers = await _context.Sellers
                    .Select(s => new
                    {
                        Id = s.Id,
                        Name = s.Name ?? "Vendeur sans nom",
                        Email = s.Email ?? "",
                        PhoneNumber = s.PhoneNumber ?? "",
                        Address = s.Address ?? "",
                        IsActive = s.IsActive,
                        IsVerified = true // Valeur par défaut pour la compatibilité
                    })
                    .ToListAsync();

                return Ok(sellers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des vendeurs", error = ex.Message });
            }
        }

        /// <summary>
        /// Récupère un vendeur par son ID utilisateur, le crée automatiquement s'il n'existe pas
        /// </summary>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <returns>Informations du vendeur</returns>
        [HttpGet("by-user/{userId:int}")]
        public async Task<IActionResult> GetSellerByUserId(int userId)
        {
            try
            {
                var seller = await _context.Sellers
                    .Where(s => s.UserId == userId.ToString())
                    .Select(s => new
                    {
                        Id = s.Id,
                        Name = s.Name ?? "Vendeur sans nom",
                        Email = s.Email ?? "",
                        PhoneNumber = s.PhoneNumber ?? "",
                        Address = s.Address ?? "",
                        IsActive = s.IsActive,
                        UserId = s.UserId
                    })
                    .FirstOrDefaultAsync();

                if (seller == null)
                {
                    // Créer automatiquement un vendeur pour cet utilisateur
                    var createdSeller = await CreateSellerForUserAsync(userId);
                    if (createdSeller != null)
                    {
                        return Ok(new
                        {
                            Id = createdSeller.Id,
                            Name = createdSeller.Name,
                            Email = createdSeller.Email,
                            PhoneNumber = createdSeller.PhoneNumber,
                            Address = createdSeller.Address,
                            IsActive = createdSeller.IsActive,
                            UserId = createdSeller.UserId
                        });
                    }

                    return NotFound(new { message = $"Impossible de créer un vendeur pour l'utilisateur {userId}" });
                }

                return Ok(seller);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération du vendeur", error = ex.Message });
            }
        }

        /// <summary>
        /// Crée un nouveau vendeur
        /// </summary>
        /// <param name="request">Données du vendeur à créer</param>
        /// <returns>Vendeur créé</returns>
        [HttpPost]
        public async Task<IActionResult> CreateSeller([FromBody] CreateSellerRequest request)
        {
            try
            {
                // Vérifier si un vendeur avec cet email existe déjà
                var existingSeller = await _context.Sellers
                    .FirstOrDefaultAsync(s => s.Email == request.Email);

                if (existingSeller != null)
                {
                    return Conflict(new { message = "Un vendeur avec cet email existe déjà" });
                }

                // Créer le nouveau vendeur
                var seller = new Domain.Models.Seller
                {
                    Name = request.Name,
                    Email = request.Email,
                    PhoneNumber = request.PhoneNumber ?? "",
                    Address = request.Address ?? "",
                    IsActive = request.IsActive,
                    UserId = request.UserId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Sellers.Add(seller);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Id = seller.Id,
                    Name = seller.Name,
                    Email = seller.Email,
                    PhoneNumber = seller.PhoneNumber,
                    Address = seller.Address,
                    IsActive = seller.IsActive,
                    UserId = seller.UserId,
                    CreatedAt = seller.CreatedAt
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la création du vendeur", error = ex.Message });
            }
        }

        /// <summary>
        /// Met à jour un vendeur existant
        /// </summary>
        /// <param name="id">ID du vendeur</param>
        /// <param name="request">Données mises à jour du vendeur</param>
        /// <returns>Vendeur mis à jour</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSeller(int id, [FromBody] UpdateSellerRequest request)
        {
            try
            {
                var existingSeller = await _context.Sellers.FindAsync(id);
                if (existingSeller == null)
                {
                    return NotFound(new { message = "Vendeur non trouvé" });
                }

                // Mettre à jour les propriétés
                existingSeller.Name = request.Name;
                existingSeller.Email = request.Email;
                existingSeller.PhoneNumber = request.PhoneNumber ?? "";
                existingSeller.Address = request.Address ?? "";
                existingSeller.IsActive = request.IsActive;
                existingSeller.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Id = existingSeller.Id,
                    Name = existingSeller.Name,
                    Email = existingSeller.Email,
                    PhoneNumber = existingSeller.PhoneNumber,
                    Address = existingSeller.Address,
                    IsActive = existingSeller.IsActive,
                    UserId = existingSeller.UserId,
                    UpdatedAt = existingSeller.UpdatedAt
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la mise à jour du vendeur", error = ex.Message });
            }
        }

        [HttpPost("profile-picture")]
        public async Task<IActionResult> UploadProfilePicture([FromBody] UploadProfilePictureRequest request)
        {
            var result = await _sellerService.UploadProfilePictureAsync(request);
            if (result == null)
            {
                return NotFound("Seller not found.");
            }
            return Ok(result);
        }

        /// <summary>
        /// Crée automatiquement un vendeur pour un utilisateur donné
        /// </summary>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <returns>Le vendeur créé ou null en cas d'erreur</returns>
        private async Task<Domain.Models.Seller?> CreateSellerForUserAsync(int userId)
        {
            try
            {
                Console.WriteLine($"Début de la création automatique du vendeur pour l'utilisateur {userId}");

                // Récupérer les informations de l'utilisateur depuis l'API Identity
                var userInfo = await GetUserInfoFromIdentityAsync(userId);
                if (userInfo == null)
                {
                    Console.WriteLine($"Impossible de récupérer les informations de l'utilisateur {userId} depuis l'API Identity");

                    // Créer un vendeur avec des informations par défaut si l'API Identity n'est pas disponible
                    Console.WriteLine($"Création d'un vendeur avec des informations par défaut pour l'utilisateur {userId}");
                    var defaultSeller = new Domain.Models.Seller
                    {
                        Name = $"Vendeur {userId}",
                        Email = $"vendeur{userId}@nafaplace.com",
                        PhoneNumber = "",
                        Address = "",
                        IsActive = true,
                        UserId = userId.ToString(),
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    _context.Sellers.Add(defaultSeller);
                    await _context.SaveChangesAsync();

                    Console.WriteLine($"Vendeur par défaut créé pour l'utilisateur {userId}: {defaultSeller.Name}");
                    return defaultSeller;
                }

                // Créer le vendeur avec les informations de l'utilisateur
                var seller = new Domain.Models.Seller
                {
                    Name = !string.IsNullOrEmpty(userInfo.FirstName) && !string.IsNullOrEmpty(userInfo.LastName)
                        ? $"{userInfo.FirstName} {userInfo.LastName}"
                        : userInfo.Username,
                    Email = userInfo.Email,
                    PhoneNumber = userInfo.PhoneNumber ?? "",
                    Address = "", // Adresse vide par défaut
                    IsActive = true,
                    UserId = userId.ToString(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Sellers.Add(seller);
                await _context.SaveChangesAsync();

                Console.WriteLine($"Vendeur créé automatiquement pour l'utilisateur {userId}: {seller.Name}");
                return seller;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la création automatique du vendeur pour l'utilisateur {userId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return null;
            }
        }

        /// <summary>
        /// Récupère les informations d'un utilisateur depuis l'API Identity
        /// </summary>
        /// <param name="userId">ID de l'utilisateur</param>
        /// <returns>Les informations de l'utilisateur ou null</returns>
        private async Task<UserInfoDto?> GetUserInfoFromIdentityAsync(int userId)
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.BaseAddress = new Uri("http://localhost:5155"); // URL de l'API Identity
                httpClient.Timeout = TimeSpan.FromSeconds(30); // Timeout de 30 secondes

                Console.WriteLine($"Tentative de récupération des informations utilisateur {userId} depuis {httpClient.BaseAddress}api/users/internal/{userId}");

                var response = await httpClient.GetAsync($"/api/users/internal/{userId}");

                Console.WriteLine($"Réponse de l'API Identity: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Données utilisateur reçues: {json}");

                    return System.Text.Json.JsonSerializer.Deserialize<UserInfoDto>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Erreur API Identity: {response.StatusCode} - {errorContent}");
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception lors de la récupération des informations utilisateur {userId}: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return null;
            }
        }
    }

    /// <summary>
    /// Modèle de requête pour créer un vendeur
    /// </summary>
    public class CreateSellerRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
        public bool IsActive { get; set; } = true;
        public string? UserId { get; set; }
    }

    /// <summary>
    /// DTO pour les informations utilisateur récupérées depuis l'API Identity
    /// </summary>
    public class UserInfoDto
    {
        public int Id { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Modèle de requête pour mettre à jour un vendeur
    /// </summary>
    public class UpdateSellerRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
